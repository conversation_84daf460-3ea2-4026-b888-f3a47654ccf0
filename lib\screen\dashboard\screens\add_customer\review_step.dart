// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';

class ReviewStep extends StatelessWidget {
  final Map<String, dynamic> borrowerInfo;
  final Map<String, dynamic> loanInfo;
  final List<Map<String, dynamic>> collateralList;
  final Map<String, dynamic>? guarantor;
  final List<Map<String, dynamic>> documents;
  final Map<String, dynamic> photos;
  final String officerName;
  final VoidCallback onBack;
  final VoidCallback onConfirm;

  const ReviewStep({
    Key? key,
    required this.borrowerInfo,
    required this.loanInfo,
    required this.collateralList,
    required this.guarantor,
    required this.documents,
    required this.photos,
    required this.officerName,
    required this.onBack,
    required this.onConfirm,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final String name = borrowerInfo['fullNameKhmer']?.toString() ?? '';

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 24, horizontal: 8),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              name.isNotEmpty ? name : 'No Name',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 12),
            ..._buildInfoRows(borrowerInfo),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                OutlinedButton(onPressed: onBack, child: const Text('Back')),
                const SizedBox(width: 12),
                ElevatedButton(
                  onPressed: onConfirm,
                  child: const Text('Confirm'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildInfoRows(Map<String, dynamic> info) {
    return info.entries
        .where(
          (e) =>
              e.key != 'fullNameKhmer' &&
              (e.value?.toString().isNotEmpty ?? false),
        )
        .map(
          (e) => Padding(
            padding: const EdgeInsets.symmetric(vertical: 2),
            child: Row(
              children: [
                Text(
                  '${_labelForKey(e.key)}: ',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                Expanded(child: Text(e.value.toString())),
              ],
            ),
          ),
        )
        .toList();
  }

  String _labelForKey(String key) {
    switch (key) {
      case 'documentType':
        return 'Document Type';
      case 'idNumber':
        return 'ID Number';
      case 'fullNameLatin':
        return 'Full Name (Latin)';
      case 'phone':
        return 'Phone';
      case 'dateOfBirth':
        return 'Date of Birth';
      default:
        return key;
    }
  }
}
