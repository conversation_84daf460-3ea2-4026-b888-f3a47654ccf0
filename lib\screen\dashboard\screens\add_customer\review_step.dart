// ignore_for_file: public_member_api_docs

import 'dart:io';
import 'package:flutter/material.dart';

class ReviewStep extends StatelessWidget {
  final Map<String, dynamic> borrowerInfo;
  final Map<String, dynamic> loanInfo;
  final List<Map<String, dynamic>> collateralList;
  final Map<String, dynamic>? guarantor;
  final List<Map<String, dynamic>> documents;
  final Map<String, dynamic> photos;
  final String officerName;
  final VoidCallback onBack;
  final VoidCallback onConfirm;

  const ReviewStep({
    Key? key,
    required this.borrowerInfo,
    required this.loanInfo,
    required this.collateralList,
    required this.guarantor,
    required this.documents,
    required this.photos,
    required this.officerName,
    required this.onBack,
    required this.onConfirm,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with customer name
        _buildHeader(context),
        const SizedBox(height: 16),

        // Basic Information sections
        _buildBorrowerInfoSection(context),
        const SizedBox(height: 16),
        _buildLoanInfoSection(context),
        if (guarantor != null) ...[
          const SizedBox(height: 16),
          _buildGuarantorSection(context),
        ],
        if (officerName.isNotEmpty) ...[
          const SizedBox(height: 16),
          _buildOfficerSection(context),
        ],
        const SizedBox(height: 24),

        // Photo Albums Section
        Text(
          'រូបថត និងឯកសារ',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: const Color(0xFF12306E),
          ),
        ),
        const SizedBox(height: 16),
        _buildPhotosSection(context),
      ],
    );
  }

  Widget _buildHeader(BuildContext context) {
    final String khmerName = borrowerInfo['fullNameKhmer']?.toString() ?? '';
    final String latinName = borrowerInfo['fullNameLatin']?.toString() ?? '';
    final String displayName = khmerName.isNotEmpty ? khmerName : latinName;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF12306E), Color(0xFF1E4A8C)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.person_outline, color: Colors.white, size: 28),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'ពិនិត្យមើលព័ត៌មាន',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      displayName.isNotEmpty ? displayName : 'No Name Provided',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBorrowerInfoSection(BuildContext context) {
    return _buildSection(
      context: context,
      title: 'ព័ត៌មានអតិថិជន',
      icon: Icons.person,
      children: [
        _buildInfoRow('ប្រភេទឯកសារ', borrowerInfo['documentType']),
        _buildInfoRow('លេខអត្តសញ្ញាណ', borrowerInfo['idNumber']),
        _buildInfoRow('ឈ្មោះពេញ (ខ្មែរ)', borrowerInfo['fullNameKhmer']),
        _buildInfoRow('ឈ្មោះពេញ (ឡាតាំង)', borrowerInfo['fullNameLatin']),
        _buildInfoRow('លេខទូរស័ព្ទ', borrowerInfo['phone']),
        _buildInfoRow('ថ្ងៃខែឆ្នាំកំណើត', borrowerInfo['dateOfBirth']),
      ],
    );
  }

  Widget _buildLoanInfoSection(BuildContext context) {
    return _buildSection(
      context: context,
      title: 'ព័ត៌មានកម្ចី',
      icon: Icons.account_balance_wallet,
      children: [
        _buildInfoRow(
          'ចំនួនទឹកប្រាក់ស្នើសុំ',
          loanInfo['requestedAmount']?.toString() != null &&
                  loanInfo['requestedAmount'].toString().isNotEmpty
              ? '\$${loanInfo['requestedAmount']}'
              : null,
        ),
        _buildInfoRow('ប្រភេទផលិតផល', loanInfo['productType']),
        _buildInfoRow('រយៈពេលកម្ចី', loanInfo['loanTerm']),
        _buildInfoRow('ថ្ងៃចេញប្រាក់', loanInfo['disbursementDate']),
      ],
    );
  }

  Widget _buildCollateralSection(BuildContext context) {
    return _buildSection(
      context: context,
      title: 'ធានា',
      icon: Icons.security,
      children:
          collateralList.isEmpty
              ? [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.grey),
                      SizedBox(width: 8),
                      Text(
                        'មិនមានធានាត្រូវបានជ្រើសរើស',
                        style: TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              ]
              : collateralList.map((collateral) {
                return Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue[200]!),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.check_circle,
                        color: Colors.blue,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          collateral['type']?.toString() ?? '',
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                      ),
                      if (collateral['image'] != null)
                        _buildImageThumbnail(collateral['image']),
                    ],
                  ),
                );
              }).toList(),
    );
  }

  Widget _buildGuarantorSection(BuildContext context) {
    if (guarantor == null) return const SizedBox.shrink();

    return _buildSection(
      context: context,
      title: 'អ្នកធានា',
      icon: Icons.people,
      children: [
        _buildInfoRow('ឈ្មោះ', guarantor!['name']),
        _buildInfoRow('លេខទូរស័ព្ទ', guarantor!['phone']),
      ],
    );
  }

  Widget _buildDocumentsSection(BuildContext context) {
    return _buildSection(
      context: context,
      title: 'ឯកសារយោង',
      icon: Icons.description,
      children:
          documents.isEmpty
              ? [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.grey),
                      SizedBox(width: 8),
                      Text(
                        'មិនមានឯកសារត្រូវបានបញ្ចូល',
                        style: TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              ]
              : documents.map((doc) {
                return Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.green[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.green[200]!),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.description,
                        color: Colors.green,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          doc['type']?.toString() ?? 'Document',
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                      ),
                      if (doc['image'] != null)
                        _buildImageThumbnail(doc['image']),
                    ],
                  ),
                );
              }).toList(),
    );
  }

  Widget _buildPhotosSection(BuildContext context) {
    return Column(
      children: [
        // Borrower Photo Album
        _buildPhotoAlbum(
          context: context,
          title: 'រូបថតអ្នកខ្ចី',
          subtitle: 'Borrower Photos',
          icon: Icons.person,
          color: Colors.blue,
          photos: {
            'borrowerNidPhoto': photos['borrowerNidPhoto'],
            'borrowerHomePhoto': photos['borrowerHomePhoto'],
            'borrowerBusinessPhoto': photos['borrowerBusinessPhoto'],
            'profilePhoto': photos['profilePhoto'],
          },
        ),
        const SizedBox(height: 16),

        // Guarantor Photo Album (if guarantor exists)
        if (guarantor != null) ...[
          _buildPhotoAlbum(
            context: context,
            title: 'រូបថតអ្នកធានា',
            subtitle: 'Guarantor Photos',
            icon: Icons.people,
            color: Colors.green,
            photos: {
              'guarantorNidPhoto': photos['guarantorNidPhoto'],
              'guarantorHomePhoto': photos['guarantorHomePhoto'],
              'guarantorBusinessPhoto': photos['guarantorBusinessPhoto'],
            },
          ),
          const SizedBox(height: 16),
        ],

        // Collateral Photo Album
        _buildCollateralPhotoAlbum(context),
        const SizedBox(height: 16),

        // Documents Album
        _buildDocumentAlbum(context),
      ],
    );
  }

  Widget _buildOfficerSection(BuildContext context) {
    return _buildSection(
      context: context,
      title: 'ព័ត៌មានមន្ត្រី',
      icon: Icons.badge,
      children: [_buildInfoRow('ឈ្មោះមន្ត្រីគ្រប់គ្រង', officerName)],
    );
  }

  // Helper methods
  Widget _buildSection({
    required BuildContext context,
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFF12306E).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: const Color(0xFF12306E), size: 20),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF12306E),
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: children,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, dynamic value) {
    if (value == null || value.toString().trim().isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
                fontSize: 14,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              value.toString(),
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 14,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImageThumbnail(String? imagePath) {
    if (imagePath == null || imagePath.isEmpty) {
      return Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Icon(
          Icons.image_not_supported,
          color: Colors.grey,
          size: 20,
        ),
      );
    }

    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Image.file(
          File(imagePath),
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              color: Colors.grey[200],
              child: const Icon(
                Icons.broken_image,
                color: Colors.grey,
                size: 20,
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildPhotoItem(String label, String imagePath) {
    return Column(
      children: [
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 6,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: Image.file(
              File(imagePath),
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: Colors.grey[200],
                  child: const Icon(
                    Icons.broken_image,
                    color: Colors.grey,
                    size: 30,
                  ),
                );
              },
            ),
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          width: 80,
          child: Text(
            label,
            textAlign: TextAlign.center,
            style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  String _getPhotoLabel(String key) {
    switch (key) {
      case 'borrowerNidPhoto':
        return 'អត្តសញ្ញាណ';
      case 'borrowerHomePhoto':
        return 'ផ្ទះ/ដី';
      case 'borrowerBusinessPhoto':
        return 'អាជីវកម្ម';
      case 'guarantorNidPhoto':
        return 'អត្តសញ្ញាណអ្នកធានា';
      case 'guarantorHomePhoto':
        return 'ផ្ទះអ្នកធានា';
      case 'guarantorBusinessPhoto':
        return 'អាជីវកម្មអ្នកធានា';
      case 'profilePhoto':
        return 'រូបថតប្រវត្តិរូប';
      default:
        return key;
    }
  }

  // Photo Album Methods
  Widget _buildPhotoAlbum({
    required BuildContext context,
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required Map<String, dynamic> photos,
  }) {
    final validPhotos =
        photos.entries
            .where(
              (entry) =>
                  entry.value != null && entry.value.toString().isNotEmpty,
            )
            .toList();

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Album Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: color, size: 20),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: color,
                        ),
                      ),
                      Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: 12,
                          color: color.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${validPhotos.length}',
                    style: TextStyle(
                      color: color,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Album Content
          Padding(
            padding: const EdgeInsets.all(16),
            child:
                validPhotos.isEmpty
                    ? _buildEmptyAlbumState(color)
                    : _buildPhotoGrid(validPhotos),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyAlbumState(Color color) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          Icon(Icons.photo_library_outlined, size: 48, color: Colors.grey[400]),
          const SizedBox(height: 12),
          Text(
            'មិនមានរូបថត',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            'No photos available',
            style: TextStyle(color: Colors.grey[500], fontSize: 12),
          ),
        ],
      ),
    );
  }

  Widget _buildPhotoGrid(List<MapEntry<String, dynamic>> photoEntries) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 0.8,
      ),
      itemCount: photoEntries.length,
      itemBuilder: (context, index) {
        final entry = photoEntries[index];
        return _buildAlbumPhotoItem(
          _getPhotoLabel(entry.key),
          entry.value.toString(),
        );
      },
    );
  }

  Widget _buildAlbumPhotoItem(String label, String imagePath) {
    return Builder(
      builder:
          (context) => GestureDetector(
            onTap: () {
              _showPhotoPreview(context, imagePath);
            },
            child: Column(
              children: [
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 6,
                          offset: const Offset(0, 3),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: Stack(
                        children: [
                          Image.file(
                            File(imagePath),
                            fit: BoxFit.cover,
                            width: double.infinity,
                            height: double.infinity,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                color: Colors.grey[200],
                                child: const Icon(
                                  Icons.broken_image,
                                  color: Colors.grey,
                                  size: 30,
                                ),
                              );
                            },
                          ),
                          // Overlay for better text visibility
                          Positioned(
                            bottom: 0,
                            left: 0,
                            right: 0,
                            child: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                    Colors.transparent,
                                    Colors.black.withValues(alpha: 0.7),
                                  ],
                                ),
                              ),
                              child: Text(
                                label,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.w600,
                                ),
                                textAlign: TextAlign.center,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
    );
  }

  Widget _buildCollateralPhotoAlbum(BuildContext context) {
    // Group collateral images by type
    final collateralPhotos = <String, String>{};

    for (final collateral in collateralList) {
      if (collateral['image'] != null &&
          collateral['image'].toString().isNotEmpty) {
        final type = collateral['type']?.toString() ?? 'Unknown';
        collateralPhotos[type] = collateral['image'].toString();
      }
    }

    return _buildPhotoAlbum(
      context: context,
      title: 'រូបថតធានា',
      subtitle: 'Collateral Photos',
      icon: Icons.security,
      color: Colors.orange,
      photos: collateralPhotos,
    );
  }

  Widget _buildDocumentAlbum(BuildContext context) {
    final documentPhotos = <String, String>{};

    for (final doc in documents) {
      if (doc['image'] != null && doc['image'].toString().isNotEmpty) {
        final type = doc['type']?.toString() ?? 'Document';
        documentPhotos[type] = doc['image'].toString();
      }
    }

    return _buildPhotoAlbum(
      context: context,
      title: 'ឯកសារយោង',
      subtitle: 'Documents',
      icon: Icons.description,
      color: Colors.purple,
      photos: documentPhotos,
    );
  }

  void _showPhotoPreview(BuildContext context, String imagePath) {
    // TODO: Implement photo preview dialog
    // For now, just show a simple dialog
    showDialog(
      context: context,
      builder:
          (context) => Dialog(
            child: Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.file(
                      File(imagePath),
                      fit: BoxFit.contain,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          height: 200,
                          color: Colors.grey[200],
                          child: const Icon(
                            Icons.broken_image,
                            color: Colors.grey,
                            size: 50,
                          ),
                        );
                      },
                    ),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Close'),
                  ),
                ],
              ),
            ),
          ),
    );
  }
}
