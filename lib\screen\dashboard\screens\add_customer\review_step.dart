// ignore_for_file: public_member_api_docs

import 'dart:io';
import 'package:flutter/material.dart';

class ReviewStep extends StatelessWidget {
  final Map<String, dynamic> borrowerInfo;
  final Map<String, dynamic> loanInfo;
  final List<Map<String, dynamic>> collateralList;
  final Map<String, dynamic>? guarantor;
  final List<Map<String, dynamic>> documents;
  final Map<String, dynamic> photos;
  final String officerName;
  final VoidCallback onBack;
  final VoidCallback onConfirm;

  const ReviewStep({
    Key? key,
    required this.borrowerInfo,
    required this.loanInfo,
    required this.collateralList,
    required this.guarantor,
    required this.documents,
    required this.photos,
    required this.officerName,
    required this.onBack,
    required this.onConfirm,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with customer name
        _buildHeader(context),
        const SizedBox(height: 16),

        // Review sections
        _buildBorrowerInfoSection(context),
        const SizedBox(height: 16),
        _buildLoanInfoSection(context),
        const SizedBox(height: 16),
        _buildCollateralSection(context),
        if (guarantor != null) ...[
          const SizedBox(height: 16),
          _buildGuarantorSection(context),
        ],
        const SizedBox(height: 16),
        _buildDocumentsSection(context),
        const SizedBox(height: 16),
        _buildPhotosSection(context),
        if (officerName.isNotEmpty) ...[
          const SizedBox(height: 16),
          _buildOfficerSection(context),
        ],
      ],
    );
  }

  Widget _buildHeader(BuildContext context) {
    final String khmerName = borrowerInfo['fullNameKhmer']?.toString() ?? '';
    final String latinName = borrowerInfo['fullNameLatin']?.toString() ?? '';
    final String displayName = khmerName.isNotEmpty ? khmerName : latinName;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF12306E), Color(0xFF1E4A8C)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.person_outline, color: Colors.white, size: 28),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'ពិនិត្យមើលព័ត៌មាន',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      displayName.isNotEmpty ? displayName : 'No Name Provided',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBorrowerInfoSection(BuildContext context) {
    return _buildSection(
      context: context,
      title: 'ព័ត៌មានអតិថិជន',
      icon: Icons.person,
      children: [
        _buildInfoRow('ប្រភេទឯកសារ', borrowerInfo['documentType']),
        _buildInfoRow('លេខអត្តសញ្ញាណ', borrowerInfo['idNumber']),
        _buildInfoRow('ឈ្មោះពេញ (ខ្មែរ)', borrowerInfo['fullNameKhmer']),
        _buildInfoRow('ឈ្មោះពេញ (ឡាតាំង)', borrowerInfo['fullNameLatin']),
        _buildInfoRow('លេខទូរស័ព្ទ', borrowerInfo['phone']),
        _buildInfoRow('ថ្ងៃខែឆ្នាំកំណើត', borrowerInfo['dateOfBirth']),
      ],
    );
  }

  Widget _buildLoanInfoSection(BuildContext context) {
    return _buildSection(
      context: context,
      title: 'ព័ត៌មានកម្ចី',
      icon: Icons.account_balance_wallet,
      children: [
        _buildInfoRow(
          'ចំនួនទឹកប្រាក់ស្នើសុំ',
          loanInfo['requestedAmount']?.toString() != null &&
                  loanInfo['requestedAmount'].toString().isNotEmpty
              ? '\$${loanInfo['requestedAmount']}'
              : null,
        ),
        _buildInfoRow('ប្រភេទផលិតផល', loanInfo['productType']),
        _buildInfoRow('រយៈពេលកម្ចី', loanInfo['loanTerm']),
        _buildInfoRow('ថ្ងៃចេញប្រាក់', loanInfo['disbursementDate']),
      ],
    );
  }

  Widget _buildCollateralSection(BuildContext context) {
    return _buildSection(
      context: context,
      title: 'ធានា',
      icon: Icons.security,
      children:
          collateralList.isEmpty
              ? [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.grey),
                      SizedBox(width: 8),
                      Text(
                        'មិនមានធានាត្រូវបានជ្រើសរើស',
                        style: TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              ]
              : collateralList.map((collateral) {
                return Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue[200]!),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.check_circle,
                        color: Colors.blue,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          collateral['type']?.toString() ?? '',
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                      ),
                      if (collateral['image'] != null)
                        _buildImageThumbnail(collateral['image']),
                    ],
                  ),
                );
              }).toList(),
    );
  }

  Widget _buildGuarantorSection(BuildContext context) {
    if (guarantor == null) return const SizedBox.shrink();

    return _buildSection(
      context: context,
      title: 'អ្នកធានា',
      icon: Icons.people,
      children: [
        _buildInfoRow('ឈ្មោះ', guarantor!['name']),
        _buildInfoRow('លេខទូរស័ព្ទ', guarantor!['phone']),
      ],
    );
  }

  Widget _buildDocumentsSection(BuildContext context) {
    return _buildSection(
      context: context,
      title: 'ឯកសារយោង',
      icon: Icons.description,
      children:
          documents.isEmpty
              ? [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.grey),
                      SizedBox(width: 8),
                      Text(
                        'មិនមានឯកសារត្រូវបានបញ្ចូល',
                        style: TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              ]
              : documents.map((doc) {
                return Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.green[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.green[200]!),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.description,
                        color: Colors.green,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          doc['type']?.toString() ?? 'Document',
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                      ),
                      if (doc['image'] != null)
                        _buildImageThumbnail(doc['image']),
                    ],
                  ),
                );
              }).toList(),
    );
  }

  Widget _buildPhotosSection(BuildContext context) {
    final photoEntries =
        photos.entries
            .where(
              (entry) =>
                  entry.value != null && entry.value.toString().isNotEmpty,
            )
            .toList();

    return _buildSection(
      context: context,
      title: 'រូបថត',
      icon: Icons.photo_library,
      children:
          photoEntries.isEmpty
              ? [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.grey),
                      SizedBox(width: 8),
                      Text(
                        'មិនមានរូបថតត្រូវបានបញ្ចូល',
                        style: TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              ]
              : [
                Wrap(
                  spacing: 12,
                  runSpacing: 12,
                  children:
                      photoEntries.map((entry) {
                        return _buildPhotoItem(
                          _getPhotoLabel(entry.key),
                          entry.value.toString(),
                        );
                      }).toList(),
                ),
              ],
    );
  }

  Widget _buildOfficerSection(BuildContext context) {
    return _buildSection(
      context: context,
      title: 'ព័ត៌មានមន្ត្រី',
      icon: Icons.badge,
      children: [_buildInfoRow('ឈ្មោះមន្ត្រីគ្រប់គ្រង', officerName)],
    );
  }

  // Helper methods
  Widget _buildSection({
    required BuildContext context,
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFF12306E).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: const Color(0xFF12306E), size: 20),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF12306E),
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: children,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, dynamic value) {
    if (value == null || value.toString().trim().isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
                fontSize: 14,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              value.toString(),
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 14,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImageThumbnail(String? imagePath) {
    if (imagePath == null || imagePath.isEmpty) {
      return Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Icon(
          Icons.image_not_supported,
          color: Colors.grey,
          size: 20,
        ),
      );
    }

    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Image.file(
          File(imagePath),
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              color: Colors.grey[200],
              child: const Icon(
                Icons.broken_image,
                color: Colors.grey,
                size: 20,
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildPhotoItem(String label, String imagePath) {
    return Column(
      children: [
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 6,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: Image.file(
              File(imagePath),
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: Colors.grey[200],
                  child: const Icon(
                    Icons.broken_image,
                    color: Colors.grey,
                    size: 30,
                  ),
                );
              },
            ),
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          width: 80,
          child: Text(
            label,
            textAlign: TextAlign.center,
            style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  String _getPhotoLabel(String key) {
    switch (key) {
      case 'borrowerNidPhoto':
        return 'អត្តសញ្ញាណ';
      case 'borrowerHomePhoto':
        return 'ផ្ទះ/ដី';
      case 'borrowerBusinessPhoto':
        return 'អាជីវកម្ម';
      case 'guarantorNidPhoto':
        return 'អត្តសញ្ញាណអ្នកធានា';
      case 'guarantorHomePhoto':
        return 'ផ្ទះអ្នកធានា';
      case 'guarantorBusinessPhoto':
        return 'អាជីវកម្មអ្នកធានា';
      case 'profilePhoto':
        return 'រូបថតប្រវត្តិរូប';
      default:
        return key;
    }
  }
}
