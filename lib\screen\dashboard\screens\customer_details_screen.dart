import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lc_work_flow/models/customer_model.dart';
import '../../../widgets/customer/customer_details_header.dart';
import '../../../widgets/customer/loan_information_section.dart';
import '../../../widgets/customer/customer_actions.dart';
import '../../../widgets/customer/customer_documents_section.dart';

class CustomerDetailsScreen extends StatelessWidget {
  final Customer customer;

  const CustomerDetailsScreen({super.key, required this.customer});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'Customer Details',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        backgroundColor: const Color(0xFF12306E),
        iconTheme: const IconThemeData(color: Colors.white),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              // TODO: Implement edit functionality
              Get.snackbar(
                'Edit Customer',
                'Edit functionality coming soon',
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: Colors.blue,
                colorText: Colors.white,
              );
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Modern Customer Header
            _buildCustomerHeader(context),
            const SizedBox(height: 16),

            // Basic Information sections
            _buildBorrowerInfoSection(context),
            const SizedBox(height: 16),
            _buildLoanInfoSection(context),
            const SizedBox(height: 16),
            if (customer.guarantorName != null &&
                customer.guarantorName!.isNotEmpty) ...[
              _buildGuarantorSection(context),
              const SizedBox(height: 16),
            ],

            const SizedBox(height: 24),

            // Photo Albums Section
            Text(
              'រូបថត និងឯកសារ',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: const Color(0xFF12306E),
              ),
            ),
            const SizedBox(height: 16),
            _buildPhotosSection(context),

            const SizedBox(height: 24),

            // Action Buttons
            _buildActionButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerHeader(BuildContext context) {
    final String displayName = customer.fullNameKhmer ?? customer.name;
    final String latinName = customer.fullNameLatin ?? customer.name;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF12306E), Color(0xFF1E4A8C)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 32,
                backgroundColor: Colors.white.withValues(alpha: 0.2),
                backgroundImage:
                    customer.profileImage != null
                        ? NetworkImage(customer.profileImage!)
                        : (customer.profilePhotoPath != null
                            ? FileImage(File(customer.profilePhotoPath!))
                            : null),
                child:
                    (customer.profileImage == null &&
                            customer.profilePhotoPath == null)
                        ? Text(
                          displayName.isNotEmpty ? displayName[0] : '',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        )
                        : null,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      displayName.isNotEmpty ? displayName : 'No Name Provided',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (latinName != displayName) ...[
                      const SizedBox(height: 4),
                      Text(
                        latinName,
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                        ),
                      ),
                    ],
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        const Icon(
                          Icons.phone,
                          color: Colors.white70,
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          customer.phone,
                          style: const TextStyle(
                            color: Colors.white70,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: customer.statusColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: customer.statusColor),
                ),
                child: Text(
                  customer.loanStatus.toString().split('.').last.toUpperCase(),
                  style: TextStyle(
                    color: customer.statusColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBorrowerInfoSection(BuildContext context) {
    return _buildSection(
      context: context,
      title: 'ព័ត៌មានអតិថិជន',
      icon: Icons.person,
      children: [
        _buildInfoRow(
          'ប្រភេទឯកសារ',
          customer.idCardType?.toString().split('.').last ?? 'N/A',
        ),
        _buildInfoRow('លេខអត្តសញ្ញាណ', customer.idNumber ?? customer.nid),
        _buildInfoRow('ឈ្មោះពេញ (ខ្មែរ)', customer.fullNameKhmer),
        _buildInfoRow(
          'ឈ្មោះពេញ (ឡាតាំង)',
          customer.fullNameLatin ?? customer.name,
        ),
        _buildInfoRow('លេខទូរស័ព្ទ', customer.phone),
        _buildInfoRow(
          'ថ្ងៃខែឆ្នាំកំណើត',
          customer.dateOfBirth?.toString().split(' ')[0],
        ),
        _buildInfoRow('មន្ត្រីគ្រប់គ្រង', customer.portfolioOfficerName),
      ],
    );
  }

  Widget _buildLoanInfoSection(BuildContext context) {
    return _buildSection(
      context: context,
      title: 'ព័ត៌មានកម្ចី',
      icon: Icons.account_balance_wallet,
      children: [
        _buildInfoRow(
          'ចំនួនទឹកប្រាក់ស្នើសុំ',
          customer.requestedAmount != null
              ? '\$${customer.requestedAmount!.toStringAsFixed(2)}'
              : '\$${customer.loanAmount.toStringAsFixed(2)}',
        ),
        _buildInfoRow('ប្រភេទផលិតផល', customer.productType?.displayName),
        _buildInfoRow('រយៈពេលកម្ចី', customer.desiredLoanTerm),
        _buildInfoRow(
          'អត្រាការប្រាក់',
          customer.interestRate != null ? '${customer.interestRate}%' : null,
        ),
        _buildInfoRow('គោលបំណងកម្ចី', customer.loanPurpose),
        _buildInfoRow(
          'ថ្ងៃចាប់ផ្តើម',
          customer.loanStartDate.toString().split(' ')[0],
        ),
        _buildInfoRow(
          'ថ្ងៃបញ្ចប់',
          customer.loanEndDate?.toString().split(' ')[0],
        ),
        _buildInfoRow(
          'ថ្ងៃចេញប្រាក់',
          customer.requestedDisbursementDate?.toString().split(' ')[0],
        ),
      ],
    );
  }

  Widget _buildGuarantorSection(BuildContext context) {
    return _buildSection(
      context: context,
      title: 'អ្នកធានា',
      icon: Icons.people,
      children: [
        _buildInfoRow('ឈ្មោះ', customer.guarantorName),
        _buildInfoRow('លេខទូរស័ព្ទ', customer.guarantorPhone),
      ],
    );
  }

  Widget _buildPhotosSection(BuildContext context) {
    return Column(
      children: [
        // Borrower Photo Album
        _buildPhotoAlbum(
          context: context,
          title: 'រូបថតអ្នកខ្ចី',
          subtitle: 'Borrower Photos',
          icon: Icons.person,
          color: Colors.blue,
          photos: {
            'borrowerNidPhoto': customer.borrowerNidPhotoPath,
            'borrowerHomePhoto': customer.borrowerHomeOrLandPhotoPath,
            'borrowerBusinessPhoto': customer.borrowerBusinessPhotoPath,
            'profilePhoto': customer.profilePhotoPath,
            'idCardPhoto': customer.idCardPhotoPath,
          },
        ),
        const SizedBox(height: 16),

        // Guarantor Photo Album (if guarantor exists)
        if (customer.guarantorName != null &&
            customer.guarantorName!.isNotEmpty) ...[
          _buildPhotoAlbum(
            context: context,
            title: 'រូបថតអ្នកធានា',
            subtitle: 'Guarantor Photos',
            icon: Icons.people,
            color: Colors.green,
            photos: {
              'guarantorNidPhoto': customer.guarantorNidPhotoPath,
              'guarantorHomePhoto': customer.guarantorHomeOrLandPhotoPath,
              'guarantorBusinessPhoto': customer.guarantorBusinessPhotoPath,
            },
          ),
          const SizedBox(height: 16),
        ],
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF12306E).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.settings,
                  color: Color(0xFF12306E),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                'Actions',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF12306E),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () {
                    Get.snackbar(
                      'Record Payment',
                      'Payment recording functionality coming soon',
                      snackPosition: SnackPosition.BOTTOM,
                      backgroundColor: Colors.green,
                      colorText: Colors.white,
                    );
                  },
                  icon: const Icon(Icons.payment),
                  label: const Text('Record Payment'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () {
                    Get.snackbar(
                      'Payment History',
                      'Payment history functionality coming soon',
                      snackPosition: SnackPosition.BOTTOM,
                      backgroundColor: Colors.blue,
                      colorText: Colors.white,
                    );
                  },
                  icon: const Icon(Icons.history),
                  label: const Text('History'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: const Color(0xFF12306E),
                    side: const BorderSide(color: Color(0xFF12306E)),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Helper methods
  Widget _buildSection({
    required BuildContext context,
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFF12306E).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: const Color(0xFF12306E), size: 20),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF12306E),
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: children,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, dynamic value) {
    if (value == null || value.toString().trim().isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
                fontSize: 14,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              value.toString(),
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 14,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhotoAlbum({
    required BuildContext context,
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required Map<String, dynamic> photos,
  }) {
    final validPhotos =
        photos.entries
            .where(
              (entry) =>
                  entry.value != null && entry.value.toString().isNotEmpty,
            )
            .toList();

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Album Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: color, size: 20),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: color,
                        ),
                      ),
                      Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: 12,
                          color: color.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${validPhotos.length}',
                    style: TextStyle(
                      color: color,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Album Content
          Padding(
            padding: const EdgeInsets.all(16),
            child:
                validPhotos.isEmpty
                    ? _buildEmptyAlbumState(color)
                    : _buildPhotoGrid(validPhotos),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyAlbumState(Color color) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          Icon(Icons.photo_library_outlined, size: 48, color: Colors.grey[400]),
          const SizedBox(height: 12),
          Text(
            'មិនមានរូបថត',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            'No photos available',
            style: TextStyle(color: Colors.grey[500], fontSize: 12),
          ),
        ],
      ),
    );
  }

  Widget _buildPhotoGrid(List<MapEntry<String, dynamic>> photoEntries) {
    if (photoEntries.length == 1) {
      return _buildSinglePhotoView(photoEntries.first);
    } else if (photoEntries.length <= 4) {
      return _buildSmallGrid(photoEntries);
    } else {
      return _buildPhotoCarousel(photoEntries);
    }
  }

  Widget _buildSinglePhotoView(MapEntry<String, dynamic> photoEntry) {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: _buildAlbumPhotoItem(
        _getPhotoLabel(photoEntry.key),
        photoEntry.value.toString(),
        isLarge: true,
      ),
    );
  }

  Widget _buildSmallGrid(List<MapEntry<String, dynamic>> photoEntries) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: photoEntries.length == 2 ? 2 : 3,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 0.8,
      ),
      itemCount: photoEntries.length,
      itemBuilder: (context, index) {
        final entry = photoEntries[index];
        return _buildAlbumPhotoItem(
          _getPhotoLabel(entry.key),
          entry.value.toString(),
        );
      },
    );
  }

  Widget _buildPhotoCarousel(List<MapEntry<String, dynamic>> photoEntries) {
    return Column(
      children: [
        // Main carousel
        SizedBox(
          height: 200,
          child: PageView.builder(
            itemCount: photoEntries.length,
            itemBuilder: (context, index) {
              final entry = photoEntries[index];
              return Container(
                margin: const EdgeInsets.symmetric(horizontal: 8),
                child: _buildAlbumPhotoItem(
                  _getPhotoLabel(entry.key),
                  entry.value.toString(),
                  isLarge: true,
                ),
              );
            },
          ),
        ),
        const SizedBox(height: 12),
        // Thumbnail strip
        SizedBox(
          height: 60,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: photoEntries.length,
            itemBuilder: (context, index) {
              final entry = photoEntries[index];
              return Container(
                margin: const EdgeInsets.only(right: 8),
                child: _buildThumbnailItem(
                  entry.value.toString(),
                  _getPhotoLabel(entry.key),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildAlbumPhotoItem(
    String label,
    String imagePath, {
    bool isLarge = false,
  }) {
    return Builder(
      builder:
          (context) => GestureDetector(
            onTap: () {
              _showPhotoPreview(context, imagePath);
            },
            child: Column(
              children: [
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(isLarge ? 16 : 12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: isLarge ? 8 : 6,
                          offset: Offset(0, isLarge ? 4 : 3),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(isLarge ? 16 : 12),
                      child: Stack(
                        children: [
                          Image.file(
                            File(imagePath),
                            fit: BoxFit.cover,
                            width: double.infinity,
                            height: double.infinity,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                color: Colors.grey[200],
                                child: Icon(
                                  Icons.broken_image,
                                  color: Colors.grey,
                                  size: isLarge ? 50 : 30,
                                ),
                              );
                            },
                          ),
                          // Overlay for better text visibility
                          Positioned(
                            bottom: 0,
                            left: 0,
                            right: 0,
                            child: Container(
                              padding: EdgeInsets.all(isLarge ? 12 : 8),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                    Colors.transparent,
                                    Colors.black.withValues(alpha: 0.7),
                                  ],
                                ),
                              ),
                              child: Text(
                                label,
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: isLarge ? 14 : 10,
                                  fontWeight: FontWeight.w600,
                                ),
                                textAlign: TextAlign.center,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ),
                          // Add a zoom indicator for large images
                          if (isLarge)
                            Positioned(
                              top: 8,
                              right: 8,
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                decoration: BoxDecoration(
                                  color: Colors.black54,
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: const Icon(
                                  Icons.zoom_in,
                                  color: Colors.white,
                                  size: 16,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
    );
  }

  Widget _buildThumbnailItem(String imagePath, String label) {
    return Builder(
      builder:
          (context) => GestureDetector(
            onTap: () {
              _showPhotoPreview(context, imagePath);
            },
            child: Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!, width: 2),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(6),
                child: Stack(
                  children: [
                    Image.file(
                      File(imagePath),
                      fit: BoxFit.cover,
                      width: double.infinity,
                      height: double.infinity,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Colors.grey[200],
                          child: const Icon(
                            Icons.broken_image,
                            color: Colors.grey,
                            size: 20,
                          ),
                        );
                      },
                    ),
                    // Small label overlay
                    Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.transparent,
                              Colors.black.withValues(alpha: 0.8),
                            ],
                          ),
                        ),
                        child: Text(
                          label,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 8,
                            fontWeight: FontWeight.w600,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
    );
  }

  String _getPhotoLabel(String key) {
    switch (key) {
      case 'borrowerNidPhoto':
        return 'អត្តសញ្ញាណ';
      case 'borrowerHomePhoto':
        return 'ផ្ទះ/ដី';
      case 'borrowerBusinessPhoto':
        return 'អាជីវកម្ម';
      case 'guarantorNidPhoto':
        return 'អត្តសញ្ញាណអ្នកធានា';
      case 'guarantorHomePhoto':
        return 'ផ្ទះអ្នកធានា';
      case 'guarantorBusinessPhoto':
        return 'អាជីវកម្មអ្នកធានា';
      case 'profilePhoto':
        return 'រូបថតប្រវត្តិរូប';
      case 'idCardPhoto':
        return 'អត្តសញ្ញាណប័ណ្ណ';
      default:
        return key;
    }
  }

  void _showPhotoPreview(BuildContext context, String imagePath) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder:
          (context) => Dialog(
            backgroundColor: Colors.transparent,
            child: Container(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.8,
                maxWidth: MediaQuery.of(context).size.width * 0.9,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Header with close button
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.8),
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(16),
                        topRight: Radius.circular(16),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Photo Preview',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        IconButton(
                          onPressed: () => Navigator.of(context).pop(),
                          icon: const Icon(Icons.close, color: Colors.white),
                        ),
                      ],
                    ),
                  ),
                  // Image container
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.9),
                        borderRadius: const BorderRadius.only(
                          bottomLeft: Radius.circular(16),
                          bottomRight: Radius.circular(16),
                        ),
                      ),
                      child: Center(
                        child: InteractiveViewer(
                          panEnabled: true,
                          boundaryMargin: const EdgeInsets.all(20),
                          minScale: 0.5,
                          maxScale: 4.0,
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.file(
                              File(imagePath),
                              fit: BoxFit.contain,
                              errorBuilder: (context, error, stackTrace) {
                                return Container(
                                  height: 200,
                                  width: 200,
                                  color: Colors.grey[800],
                                  child: const Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.broken_image,
                                        color: Colors.white,
                                        size: 50,
                                      ),
                                      SizedBox(height: 8),
                                      Text(
                                        'Image not found',
                                        style: TextStyle(color: Colors.white),
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
        ),
      ),
    );
  }
}
