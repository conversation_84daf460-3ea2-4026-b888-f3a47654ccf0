import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'dart:io';

import '../../../models/customer_model.dart';

class CustomerCard extends StatelessWidget {
  final Customer customer;
  final VoidCallback onTap;

  const CustomerCard({super.key, required this.customer, required this.onTap});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final currencyFormat = NumberFormat.currency(
      locale: 'km',
      symbol: '៛',
      decimalDigits: 0,
    );

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.grey[200]!, width: 1),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Enhanced Avatar with status indicator
                  Stack(
                    children: [
                      CircleAvatar(
                        radius: 28,
                        backgroundColor: const Color(0xFFF0F4FF),
                        backgroundImage:
                            customer.profileImage != null
                                ? NetworkImage(customer.profileImage!)
                                : (customer.profilePhotoPath != null
                                    ? FileImage(
                                      File(customer.profilePhotoPath!),
                                    )
                                    : null),
                        child:
                            (customer.profileImage == null &&
                                    customer.profilePhotoPath == null)
                                ? Text(
                                  customer.name.isNotEmpty
                                      ? customer.name[0]
                                      : '',
                                  style: textTheme.titleLarge?.copyWith(
                                    color: const Color(0xFF12306E),
                                    fontWeight: FontWeight.bold,
                                    fontSize: 20,
                                  ),
                                )
                                : null,
                      ),
                      // Status indicator dot
                      Positioned(
                        bottom: 2,
                        right: 2,
                        child: Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color: customer.statusColor,
                            shape: BoxShape.circle,
                            border: Border.all(color: Colors.white, width: 2),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              child: Text(
                                customer.name,
                                style: textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: Colors.black87,
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                            const SizedBox(width: 8),
                            _buildStatusBadge(customer),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(
                              Icons.phone,
                              size: 12,
                              color: Colors.grey[600],
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                customer.phone,
                                style: textTheme.bodySmall?.copyWith(
                                  color: Colors.grey[600],
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Row(
                            children: [
                              _buildInfoChip(
                                currencyFormat.format(customer.loanAmount),
                                Icons.money,
                                theme.primaryColor,
                              ),
                              if (customer.interestRate != null) ...[
                                const SizedBox(width: 8),
                                _buildInfoChip(
                                  '${customer.interestRate}%',
                                  Icons.percent,
                                  Colors.orange,
                                ),
                              ],
                              if (customer.loanPurpose != null) ...[
                                const SizedBox(width: 8),
                                _buildInfoChip(
                                  customer.loanPurpose!,
                                  Icons.description,
                                  Colors.teal,
                                ),
                              ],
                              const SizedBox(width: 8),
                              _buildInfoChip(
                                customer.formattedStartDate,
                                Icons.calendar_today,
                                Colors.blue,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(Customer customer) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: customer.statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: customer.statusColor),
      ),
      child: Text(
        customer.loanStatus.toString().split('.').last.toUpperCase(),
        style: TextStyle(
          color: customer.statusColor,
          fontSize: 10,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildInfoChip(String text, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: color),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              color: color,
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
